"use client"

import { useState, useRef } from "react"
import { motion, useInView } from "framer-motion"
import skillsData from "@/data/skills.json"

interface Skill {
  id: number
  title: string
  description: string
  color: string
}

export function SkillGraph() {
  const [hoveredSkill, setHoveredSkill] = useState<number | null>(null)
  const [clickedSkill, setClickedSkill] = useState<number | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)
  const isInView = useInView(containerRef, { once: true, amount: 0.3 })

  // Central node position
  const centerNode = { x: 400, y: 80 }

  // Fixed tree structure positions for consistent layout
  const generateTreePositions = () => {
    const positions = [
      // First row (3 cards)
      { x: 200, y: 200, skill: skillsData.skills[0] }, // Frontend Development
      { x: 400, y: 180, skill: skillsData.skills[1] }, // UI/UX Design
      { x: 600, y: 200, skill: skillsData.skills[2] }, // Backend Development

      // Second row (2 cards)
      { x: 300, y: 320, skill: skillsData.skills[3] }, // DevOps & Tools
      { x: 500, y: 320, skill: skillsData.skills[4] }, // AI & Automation
    ]
    return positions
  }

  // Generate fixed tree positions
  const skillPositions = generateTreePositions()

  // Generate curved path for connection lines
  const generateCurvePath = (startX: number, startY: number, endX: number, endY: number) => {
    const controlX = (startX + endX) / 2
    const controlY = (startY + endY) / 2 - 50
    return `M ${startX} ${startY} Q ${controlX} ${controlY} ${endX} ${endY}`
  }

  return (
    <div ref={containerRef} className="relative w-full max-w-6xl mx-auto h-[500px] overflow-visible">
      {/* SVG for connections and central node */}
      <svg
        className="absolute inset-0 w-full h-full pointer-events-none z-10"
        viewBox="0 0 800 500"
        preserveAspectRatio="xMidYMid meet"
      >
        <defs>
          <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="100%">
            <stop offset="0%" stopColor="#ffffff" stopOpacity="0.8" />
            <stop offset="50%" stopColor="#3B82F6" stopOpacity="1" />
            <stop offset="100%" stopColor="#ffffff" stopOpacity="0.6" />
          </linearGradient>
          <filter id="glow">
            <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
            <feMerge>
              <feMergeNode in="coloredBlur"/>
              <feMergeNode in="SourceGraphic"/>
            </feMerge>
          </filter>
        </defs>

        {/* Central Node */}
        <motion.circle
          cx={centerNode.x}
          cy={centerNode.y}
          r="15"
          fill="#3B82F6"
          filter="url(#glow)"
          initial={{ scale: 0, opacity: 0 }}
          animate={isInView ? { scale: 1, opacity: 1 } : { scale: 0, opacity: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
          className="drop-shadow-lg"
        />

        {/* Connection Lines - All 5 lines to each card */}
        {skillPositions.map((position, index) => (
          <motion.path
            key={`line-${index}`}
            d={generateCurvePath(centerNode.x, centerNode.y, position.x, position.y)}
            stroke={hoveredSkill === position.skill.id ? position.skill.color : "url(#lineGradient)"}
            strokeWidth={hoveredSkill === position.skill.id ? "4" : "2"}
            fill="none"
            filter="url(#glow)"
            initial={{ pathLength: 0, opacity: 0 }}
            animate={isInView ? {
              pathLength: 1,
              opacity: 0.9
            } : { pathLength: 0, opacity: 0 }}
            transition={{
              pathLength: { duration: 1.5, delay: 1 + index * 0.3 },
              opacity: { duration: 0.5, delay: 1 + index * 0.3 }
            }}
            className="transition-all duration-300"
          />
        ))}
      </svg>



      {/* Skill Cards */}
      {skillPositions.map((position, index) => (
        <motion.div
          key={`card-${index}`}
          className="absolute z-30"
          style={{
            left: position.x - 120, // Center the card (240px width / 2)
            top: position.y - 60,   // Center the card (120px height / 2)
          }}
          initial={{ opacity: 0, scale: 0, y: 50 }}
          animate={isInView ? {
            opacity: 1,
            scale: 1,
            y: 0
          } : { opacity: 0, scale: 0, y: 50 }}
          transition={{
            duration: 0.6,
            delay: 1.5 + index * 0.2,
            type: "spring",
            stiffness: 100
          }}
          whileHover={{
            scale: 1.05,
            y: -8,
            transition: { duration: 0.2 }
          }}
          whileTap={{ scale: 0.95 }}
          onHoverStart={() => setHoveredSkill(position.skill.id)}
          onHoverEnd={() => setHoveredSkill(null)}
          onClick={() => setClickedSkill(clickedSkill === position.skill.id ? null : position.skill.id)}
        >
          <div
            className={`
              w-60 h-32 rounded-xl p-4 cursor-pointer transition-all duration-300
              bg-black/40 backdrop-blur-lg border border-white/20
              hover:border-white/40 hover:bg-black/60
              ${hoveredSkill === position.skill.id || clickedSkill === position.skill.id ? 'neo-glow' : ''}
              ${clickedSkill === position.skill.id ? 'ring-2 ring-white/30' : ''}
            `}
            style={{
              boxShadow: (hoveredSkill === position.skill.id || clickedSkill === position.skill.id)
                ? `0 0 25px ${position.skill.color}50, 0 0 50px ${position.skill.color}30`
                : '0 4px 20px rgba(0, 0, 0, 0.3)'
            }}
          >
            <div className="flex flex-col h-full justify-between">
              <div>
                <h4 className="text-lg font-semibold text-white font-space-grotesk mb-2">
                  {position.skill.title}
                </h4>
                <p className="text-sm text-gray-300 line-clamp-2">
                  {position.skill.description}
                </p>
              </div>
              <div
                className="w-full h-1.5 rounded-full mt-3"
                style={{ backgroundColor: position.skill.color }}
              />
            </div>

            {/* Click indicator */}
            {clickedSkill === position.skill.id && (
              <motion.div
                className="absolute -top-2 -right-2 w-5 h-5 bg-white rounded-full flex items-center justify-center"
                initial={{ scale: 0 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0 }}
              >
                <div className="w-2.5 h-2.5 bg-blue-500 rounded-full animate-pulse" />
              </motion.div>
            )}
          </div>
        </motion.div>
      ))}

      {/* Tooltip for clicked skill */}
      {clickedSkill && (
        <motion.div
          className="absolute bottom-4 left-1/2 transform -translate-x-1/2 z-40"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
        >
          <div className="bg-black/80 backdrop-blur-lg border border-white/30 rounded-lg p-4 max-w-xs">
            <p className="text-white text-sm text-center">
              Click on skill cards to explore them!
              <br />
              <span className="text-gray-400 text-xs">
                (Future: Detailed skill information will appear here)
              </span>
            </p>
          </div>
        </motion.div>
      )}
    </div>
  )
}
